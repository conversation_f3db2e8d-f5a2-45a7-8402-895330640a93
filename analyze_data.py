#!/usr/bin/env python3
"""
Example script to analyze the scraped Jable.TV watch later data.
This shows how to work with the JSON output from the main scraper.
"""

import json
import os
from collections import Counter
import re

def load_latest_data():
    """Load the most recent JSON file."""
    json_files = [f for f in os.listdir('.') if f.startswith('jable_watch_later_') and f.endswith('.json')]
    if not json_files:
        print("No JSON files found. Run main.py first.")
        return None
    
    latest_file = sorted(json_files)[-1]
    print(f"Loading data from: {latest_file}")
    
    with open(latest_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_videos(videos):
    """Analyze the video data and print statistics."""
    if not videos:
        print("No videos to analyze.")
        return
    
    print(f"\n📊 ANALYSIS RESULTS")
    print("=" * 50)
    print(f"Total videos: {len(videos)}")
    
    # View statistics
    views = [int(v['views'].replace(',', '').replace(' ', '')) for v in videos if v['views'].isdigit() or v['views'].replace(',', '').replace(' ', '').isdigit()]
    if views:
        print(f"Total views: {sum(views):,}")
        print(f"Average views per video: {sum(views) // len(views):,}")
        print(f"Most viewed: {max(views):,}")
        print(f"Least viewed: {min(views):,}")
    
    # Like statistics
    likes = [int(v['likes'].replace(',', '').replace(' ', '')) for v in videos if v['likes'].isdigit() or v['likes'].replace(',', '').replace(' ', '').isdigit()]
    if likes:
        print(f"Total likes: {sum(likes):,}")
        print(f"Average likes per video: {sum(likes) // len(likes):,}")
    
    # Extract video codes/series
    video_codes = []
    for video in videos:
        # Extract video code from title (e.g., "SONE-749", "DASS-654")
        code_match = re.search(r'\b([A-Z]{2,6}-\d{3,4})\b', video['title'])
        if code_match:
            video_codes.append(code_match.group(1))
    
    if video_codes:
        print(f"\n🏷️ VIDEO SERIES ANALYSIS")
        print("-" * 30)
        series_counter = Counter([code.split('-')[0] for code in video_codes])
        print("Top 10 series:")
        for series, count in series_counter.most_common(10):
            print(f"  {series}: {count} videos")
    
    # Top videos by views
    print(f"\n🔥 TOP 10 MOST VIEWED VIDEOS")
    print("-" * 50)
    sorted_videos = sorted(videos, key=lambda x: int(x['views'].replace(',', '').replace(' ', '')) if x['views'].replace(',', '').replace(' ', '').isdigit() else 0, reverse=True)
    for i, video in enumerate(sorted_videos[:10], 1):
        print(f"{i}. {video['title'][:60]}...")
        print(f"   Views: {video['views']} | Likes: {video['likes']}")
        print()

def export_filtered_data(videos, min_views=100000):
    """Export videos with views above a threshold."""
    filtered = [v for v in videos if int(v['views'].replace(',', '').replace(' ', '')) >= min_views if v['views'].replace(',', '').replace(' ', '').isdigit()]
    
    if filtered:
        filename = f"high_view_videos_{min_views//1000}k_plus.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(filtered, f, ensure_ascii=False, indent=2)
        print(f"\n💾 Exported {len(filtered)} videos with {min_views:,}+ views to {filename}")

def search_videos(videos, search_term):
    """Search for videos containing a specific term."""
    results = [v for v in videos if search_term.lower() in v['title'].lower()]
    
    if results:
        print(f"\n🔍 SEARCH RESULTS for '{search_term}'")
        print("-" * 50)
        for i, video in enumerate(results, 1):
            print(f"{i}. {video['title']}")
            print(f"   Views: {video['views']} | Likes: {video['likes']}")
            print(f"   URL: {video['url']}")
            print()
    else:
        print(f"No videos found containing '{search_term}'")

if __name__ == "__main__":
    # Load the data
    videos = load_latest_data()
    if not videos:
        exit(1)
    
    # Run analysis
    analyze_videos(videos)
    
    # Export high-view videos
    export_filtered_data(videos, min_views=500000)  # 500k+ views
    
    # Example search
    print("\n" + "="*60)
    search_term = input("Enter a search term (or press Enter to skip): ").strip()
    if search_term:
        search_videos(videos, search_term)
    
    print("\n✅ Analysis complete!")

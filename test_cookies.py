#!/usr/bin/env python3
"""
Simple script to test if your cookies.txt file is working correctly.
Run this before using the main scraper to validate your authentication.
"""

import os
import requests
from http.cookiejar import MozillaCookieJar

def test_cookies(cookies_file='cookies.txt'):
    """Test if cookies are valid for Jable.TV authentication."""
    
    print("🍪 COOKIE VALIDATION TEST")
    print("=" * 50)
    
    # Check if cookies file exists
    if not os.path.exists(cookies_file):
        print(f"❌ Error: {cookies_file} not found!")
        print("Please export your browser cookies first.")
        print("See COOKIE_SETUP_GUIDE.md for instructions.")
        return False
    
    # Load cookies
    print(f"📂 Loading cookies from {cookies_file}...")
    try:
        cookie_jar = MozillaCookieJar(cookies_file)
        cookie_jar.load(ignore_discard=True, ignore_expires=True)
        print(f"✅ Loaded {len(cookie_jar)} cookies")
        
        # Show cookie domains
        domains = set()
        for cookie in cookie_jar:
            domains.add(cookie.domain)
        print(f"🌐 Cookie domains: {', '.join(domains)}")
        
    except Exception as e:
        print(f"❌ Error loading cookies: {e}")
        return False
    
    # Test with simple requests
    print(f"\n🌐 Testing connection to Jable.TV...")
    
    session = requests.Session()
    session.cookies.update(cookie_jar)
    
    # Add headers to mimic browser
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # Test main page first
        print("📡 Testing main page...")
        response = session.get('https://jable.tv/', headers=headers, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Main page accessible")
        else:
            print(f"⚠️  Main page returned {response.status_code}")
        
        # Test watch later page
        print("📡 Testing watch later page...")
        watch_later_url = 'https://jable.tv/my/favourites/videos-watch-later/'
        response = session.get(watch_later_url, headers=headers, timeout=30)
        print(f"   Status: {response.status_code}")
        
        # Check response content
        if "Just a moment" in response.text:
            print("❌ Cloudflare challenge detected")
            print("   Your cookies might be invalid or expired")
            return False
        elif "login" in response.url.lower() or "登入" in response.text:
            print("❌ Redirected to login page")
            print("   Your session has expired - please login and re-export cookies")
            return False
        elif "video-img-box" in response.text:
            print("✅ Watch later page accessible!")
            print("✅ Video content detected!")
            
            # Try to count videos on first page
            video_count = response.text.count('video-img-box')
            print(f"📊 Found approximately {video_count} videos on first page")
            return True
        else:
            print("⚠️  Unexpected response content")
            print("   First 200 characters:")
            print(f"   {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        print("   Check your internet connection and proxy settings")
        return False

def main():
    """Main function to run cookie test."""
    
    success = test_cookies()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS! Your cookies are working correctly.")
        print("✅ You can now run the main scraper: python main.py")
    else:
        print("❌ FAILED! Your cookies need to be fixed.")
        print("📖 Please follow the COOKIE_SETUP_GUIDE.md instructions")
        print("🔄 Steps to fix:")
        print("   1. Login to jable.tv in your browser")
        print("   2. Navigate to your watch later page")
        print("   3. Export cookies using browser extension")
        print("   4. Replace cookies.txt with exported file")
        print("   5. Run this test again")

if __name__ == "__main__":
    main()

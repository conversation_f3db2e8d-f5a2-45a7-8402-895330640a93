#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to verify the expected video count calculation.
This helps validate that our scraping results match expectations.
"""

from main import parse_videos_from_html, get_total_pages

def verify_expected_count():
    """Verify the expected video count calculation."""
    
    # Load the sample HTML file
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    if not html_files:
        print("No HTML files found.")
        return
    
    html_file = html_files[0]
    print(f"Analyzing: {html_file}")
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Parse the data
    videos = parse_videos_from_html(html_content)
    total_pages = get_total_pages(html_content)
    videos_per_page = len(videos)
    
    print(f"\n📊 CALCULATION VERIFICATION:")
    print(f"   Videos on sample page: {videos_per_page}")
    print(f"   Total pages detected: {total_pages}")
    print(f"   Expected total videos: {total_pages} × {videos_per_page} = {total_pages * videos_per_page}")
    
    print(f"\n📋 SAMPLE VIDEO CODES:")
    for i, video in enumerate(videos[:10], 1):
        from main import extract_video_code
        code = extract_video_code(video)
        print(f"   {i:2d}. {code}")
    
    if len(videos) > 10:
        print(f"   ... and {len(videos) - 10} more videos on this page")
    
    print(f"\n✅ If all pages have {videos_per_page} videos, you should get {total_pages * videos_per_page} total videos.")
    print(f"✅ The last page might have fewer videos, so the actual count could be slightly less.")

if __name__ == "__main__":
    import os
    verify_expected_count()

import cloudscraper
import json
import csv
import re
import time
from http.cookiejar import MozillaCookieJar
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import os
from datetime import datetime

# Configuration
USERNAME = "hostszg"
PASSWORD = "ce0dc997"
BASE_URL = "https://jable.tv"
WATCH_LATER_URL = "https://jable.tv/my/favourites/videos-watch-later/"

def setup_scraper(cookies_file='cookies.txt', proxy_address=None):
    """
    Initialize cloudscraper with proper configuration.

    Args:
        cookies_file (str): Path to cookies file
        proxy_address (str): Proxy address in format 'IP:PORT'

    Returns:
        cloudscraper.CloudScraper: Configured scraper instance
    """
    # Configure proxies
    proxies = None
    if proxy_address:
        if not proxy_address.startswith(('http://', 'https://', 'socks5://', 'socks4://')):
            proxy_url = f"http://{proxy_address}"
        else:
            proxy_url = proxy_address

        proxies = {
            "http": proxy_url,
            "https": proxy_url,
        }
        print(f"Configuring proxy: {proxy_url}")

    # Initialize cloudscraper
    print("Initializing cloudscraper...")
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'desktop': True
        },
        delay=25,
        debug=True
    )

    # Set proxies after initialization
    if proxies:
        scraper.proxies.update(proxies)

    # Load cookies
    if os.path.exists(cookies_file):
        print(f"Loading cookies from {cookies_file}...")
        cookie_jar = MozillaCookieJar(cookies_file)
        try:
            cookie_jar.load(ignore_discard=True, ignore_expires=True)
            scraper.cookies.update(cookie_jar)
            print("Cookies loaded successfully.")
        except Exception as e:
            print(f"Error loading cookies: {e}")
    else:
        print(f"Cookies file '{cookies_file}' not found.")

    return scraper

def extract_video_info(video_element):
    """
    Extract video information from a video HTML element.

    Args:
        video_element: BeautifulSoup element containing video info

    Returns:
        dict: Video information
    """
    try:
        # Extract video URL and ID
        link_element = video_element.find('a', href=True)
        if not link_element:
            return None

        video_url = link_element['href']
        if video_url.startswith('/'):
            video_url = urljoin(BASE_URL, video_url)

        # Extract video ID from URL
        video_id_match = re.search(r'/videos/([^/]+)/', video_url)
        video_id = video_id_match.group(1) if video_id_match else "unknown"

        # Extract title
        title_element = video_element.find('h6', class_='title')
        title = title_element.get_text(strip=True) if title_element else "No title"

        # Extract views and likes from sub-title
        sub_title = video_element.find('p', class_='sub-title')
        views = "0"
        likes = "0"

        if sub_title:
            # Get all text nodes, splitting by SVG elements
            text_parts = []
            for element in sub_title.children:
                if hasattr(element, 'string') and element.string:
                    text_parts.append(element.string.strip())
                elif hasattr(element, 'get_text'):
                    text = element.get_text(strip=True)
                    if text and not text.startswith('<'):
                        text_parts.append(text)

            # Filter out empty strings and find numbers
            numbers = []
            for part in text_parts:
                if part and re.match(r'^\d+(?:\s+\d+)*$', part):
                    numbers.append(part.replace(' ', ''))

            if len(numbers) >= 1:
                views = numbers[0]
            if len(numbers) >= 2:
                likes = numbers[1]

        # Extract thumbnail URL
        img_element = video_element.find('img')
        thumbnail_url = ""
        if img_element:
            thumbnail_url = img_element.get('data-src') or img_element.get('src', '')
            if thumbnail_url.startswith('/'):
                thumbnail_url = urljoin(BASE_URL, thumbnail_url)

        return {
            'video_id': video_id,
            'title': title,
            'url': video_url,
            'views': views,
            'likes': likes,
            'thumbnail_url': thumbnail_url
        }
    except Exception as e:
        print(f"Error extracting video info: {e}")
        return None

def get_page_content(scraper, url, page_num=1):
    """
    Fetch content from a specific page using AJAX pagination.

    Args:
        scraper: CloudScraper instance
        url (str): Base URL
        page_num (int): Page number to fetch

    Returns:
        str or None: HTML content if successful
    """
    try:
        if page_num == 1:
            page_url = url
        else:
            # For Jable.TV pagination, we need to use AJAX format
            # Based on the HTML structure, pages use from_my_fav_videos parameter
            page_url = f"{url}?from_my_fav_videos={page_num:02d}"

        print(f"Fetching page {page_num}: {page_url}")

        # Add headers to mimic browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Referer': url,
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        response = scraper.get(page_url, headers=headers)
        response.raise_for_status()

        # Check for Cloudflare challenge
        if "Just a moment..." in response.text or "Enable JavaScript and cookies to continue" in response.text:
            print(f"Cloudflare challenge detected on page {page_num}")
            return None

        # Check if we're redirected to login page
        if "login" in response.url.lower() or "登入" in response.text:
            print(f"Login required - redirected to login page on page {page_num}")
            return None

        # Check if page has video content
        if "video-img-box" not in response.text:
            print(f"No video content found on page {page_num} - might be end of pagination")
            return None

        return response.text

    except Exception as e:
        print(f"Error fetching page {page_num}: {e}")
        return None

def parse_videos_from_html(html_content):
    """
    Parse video information from HTML content.

    Args:
        html_content (str): HTML content of the page

    Returns:
        list: List of video dictionaries
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        videos = []

        # Find all video containers
        video_containers = soup.find_all('div', class_='video-img-box')

        for container in video_containers:
            video_info = extract_video_info(container)
            if video_info:
                videos.append(video_info)

        return videos

    except Exception as e:
        print(f"Error parsing HTML: {e}")
        return []

def get_total_pages(html_content):
    """
    Extract total number of pages from pagination.

    Args:
        html_content (str): HTML content

    Returns:
        int: Total number of pages
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # Look for the "最後" (last) page link
        last_page_link = soup.find('a', string=re.compile(r'最後'))
        if last_page_link:
            # Extract page number from data-parameters
            data_params = last_page_link.get('data-parameters', '')
            page_match = re.search(r'from_my_fav_videos:(\d+)', data_params)
            if page_match:
                return int(page_match.group(1))

        # Fallback: look for all page links
        page_links = soup.find_all('a', class_='page-link')
        max_page = 1
        for link in page_links:
            data_params = link.get('data-parameters', '')
            page_match = re.search(r'from_my_fav_videos:(\d+)', data_params)
            if page_match:
                page_num = int(page_match.group(1))
                max_page = max(max_page, page_num)

        return max_page

    except Exception as e:
        print(f"Error extracting total pages: {e}")
        return 1

def save_videos_to_files(videos, base_filename="jable_watch_later"):
    """
    Save videos to multiple file formats.

    Args:
        videos (list): List of video dictionaries
        base_filename (str): Base filename for output files
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save to JSON
    json_filename = f"{base_filename}_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(videos, f, ensure_ascii=False, indent=2)
    print(f"Saved {len(videos)} videos to {json_filename}")

    # Save to CSV
    csv_filename = f"{base_filename}_{timestamp}.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
        if videos:
            writer = csv.DictWriter(f, fieldnames=videos[0].keys())
            writer.writeheader()
            writer.writerows(videos)
    print(f"Saved {len(videos)} videos to {csv_filename}")

    # Save to TXT (detailed format)
    txt_filename = f"{base_filename}_{timestamp}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as f:
        f.write(f"Jable.TV Watch Later List - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        for i, video in enumerate(videos, 1):
            f.write(f"{i}. {video['title']}\n")
            f.write(f"   URL: {video['url']}\n")
            f.write(f"   Views: {video['views']} | Likes: {video['likes']}\n")
            f.write(f"   Video ID: {video['video_id']}\n")
            f.write("-" * 80 + "\n")
    print(f"Saved {len(videos)} videos to {txt_filename}")

    # Save video codes only (NEW)
    codes_filename = f"{base_filename}_codes_{timestamp}.txt"
    with open(codes_filename, 'w', encoding='utf-8') as f:
        f.write(f"Jable.TV Video Codes - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total: {len(videos)} videos\n")
        f.write("=" * 50 + "\n\n")

        for video in videos:
            # Extract video code from video_id or title
            video_code = extract_video_code(video)
            if video_code:
                f.write(f"{video_code}\n")
            else:
                # Fallback to video_id if no code found
                f.write(f"{video['video_id']}\n")

    print(f"Saved {len(videos)} video codes to {codes_filename}")

def extract_video_code(video):
    """
    Extract video code (like SONE-749) from video data.

    Args:
        video (dict): Video dictionary

    Returns:
        str: Video code or None if not found
    """
    # Try to extract from title first
    title = video.get('title', '')
    code_match = re.search(r'\b([A-Z]{2,6}-\d{3,4})\b', title)
    if code_match:
        return code_match.group(1)

    # Try to extract from video_id
    video_id = video.get('video_id', '')
    code_match = re.search(r'\b([A-Z]{2,6}-\d{3,4})\b', video_id.upper())
    if code_match:
        return code_match.group(1)

    # If no standard code found, return the video_id in uppercase
    return video_id.upper() if video_id else None

def scrape_all_watch_later_videos(cookies_file='cookies.txt', proxy_address=None):
    """
    Main function to scrape all watch later videos.

    Args:
        cookies_file (str): Path to cookies file
        proxy_address (str): Proxy address

    Returns:
        tuple: (list of all videos, scraping statistics)
    """
    print("Starting Jable.TV Watch Later Scraper...")

    # Setup scraper
    scraper = setup_scraper(cookies_file, proxy_address)

    # Get first page to determine total pages
    print("Fetching first page to determine pagination...")
    first_page_content = get_page_content(scraper, WATCH_LATER_URL, 1)

    if not first_page_content:
        print("Failed to fetch first page. Check your cookies and connection.")
        return [], {}

    # Parse first page
    all_videos = parse_videos_from_html(first_page_content)
    total_pages = get_total_pages(first_page_content)
    videos_per_page = len(all_videos)

    print(f"Found {len(all_videos)} videos on page 1")
    print(f"Total pages to scrape: {total_pages}")
    print(f"Expected total videos: {total_pages * videos_per_page} (if all pages have {videos_per_page} videos)")

    # Statistics tracking
    stats = {
        'total_pages': total_pages,
        'videos_per_page': videos_per_page,
        'expected_total': total_pages * videos_per_page,
        'pages_scraped': 1,
        'pages_failed': 0,
        'videos_by_page': {1: len(all_videos)}
    }

    # Scrape remaining pages
    for page_num in range(2, total_pages + 1):
        print(f"\nScraping page {page_num}/{total_pages}...")

        # Add delay between requests to be respectful
        time.sleep(2)

        page_content = get_page_content(scraper, WATCH_LATER_URL, page_num)
        if page_content:
            page_videos = parse_videos_from_html(page_content)
            all_videos.extend(page_videos)
            stats['pages_scraped'] += 1
            stats['videos_by_page'][page_num] = len(page_videos)
            print(f"Found {len(page_videos)} videos on page {page_num}")

            # Check if page has fewer videos (might be last page)
            if len(page_videos) < videos_per_page:
                print(f"Page {page_num} has fewer videos ({len(page_videos)}) - might be the last page")
        else:
            print(f"Failed to fetch page {page_num}")
            stats['pages_failed'] += 1

    # Final statistics
    stats['actual_total'] = len(all_videos)
    stats['success_rate'] = (stats['pages_scraped'] / total_pages) * 100

    print(f"\n" + "="*60)
    print(f"SCRAPING COMPLETED!")
    print(f"="*60)
    print(f"Total pages: {stats['total_pages']}")
    print(f"Pages scraped successfully: {stats['pages_scraped']}")
    print(f"Pages failed: {stats['pages_failed']}")
    print(f"Success rate: {stats['success_rate']:.1f}%")
    print(f"Expected videos: {stats['expected_total']}")
    print(f"Actual videos found: {stats['actual_total']}")

    if stats['actual_total'] == stats['expected_total']:
        print("✅ Perfect match! All expected videos were scraped.")
    elif stats['actual_total'] > stats['expected_total'] * 0.95:
        print("✅ Good result! Got most expected videos.")
    else:
        print("⚠️  Warning: Significantly fewer videos than expected.")

    return all_videos, stats

# --- Main execution ---
if __name__ == "__main__":
    COOKIES_FILE = 'cookies.txt'
    YOUR_PROXY_ADDRESS = "************:7890"  # Your proxy here

    # Create dummy cookies file if it doesn't exist
    if not os.path.exists(COOKIES_FILE):
        print(f"Creating a dummy '{COOKIES_FILE}' for demonstration purposes.")
        print(f"PLEASE NOTE: This dummy file contains NO actual cookies and will NOT bypass authentication.")
        print(f"You NEED to replace its content with valid cookies exported from your browser.")
        with open(COOKIES_FILE, 'w') as f:
            f.write("# Netscape HTTP Cookie File\n")
            f.write("# This is a dummy file. Replace with real cookies from jable.tv after login.\n")
            f.write(".jable.tv\tTRUE\t/\tFALSE\t0\t__cf_bm\t1234567890abcdef\n")
            f.write(".jable.tv\tTRUE\t/\tFALSE\t0\tPHPSESSID\tabcdef1234567890\n")
        print("Dummy cookies.txt created.")
    else:
        print(f"'{COOKIES_FILE}' already exists. Ensure it contains valid cookies from your jable.tv session.")

    print("\n" + "="*80)
    print("JABLE.TV WATCH LATER SCRAPER")
    print("="*80)

    try:
        # Scrape all videos
        all_videos, stats = scrape_all_watch_later_videos(COOKIES_FILE, YOUR_PROXY_ADDRESS)

        if all_videos:
            print(f"\n✅ Successfully scraped {len(all_videos)} videos!")

            # Display detailed statistics
            print(f"\n📊 DETAILED STATISTICS:")
            print(f"   Total pages processed: {stats['pages_scraped']}/{stats['total_pages']}")
            print(f"   Average videos per page: {len(all_videos) / stats['pages_scraped']:.1f}")
            print(f"   Success rate: {stats['success_rate']:.1f}%")

            # Show videos per page breakdown
            print(f"\n📄 Videos per page breakdown:")
            for page, count in sorted(stats['videos_by_page'].items()):
                print(f"   Page {page}: {count} videos")

            # Save to files
            print(f"\n💾 Saving files...")
            save_videos_to_files(all_videos)

            # Display sample
            print(f"\n📋 Sample of scraped videos:")
            for i, video in enumerate(all_videos[:5], 1):
                video_code = extract_video_code(video)
                print(f"{i}. [{video_code}] {video['title'][:50]}...")
                print(f"   Views: {video['views']} | Likes: {video['likes']}")
                print(f"   URL: {video['url']}")
                print()

            if len(all_videos) > 5:
                print(f"... and {len(all_videos) - 5} more videos")

            # Validation check
            print(f"\n🔍 VALIDATION:")
            expected = stats.get('expected_total', 0)
            actual = len(all_videos)
            if expected > 0:
                percentage = (actual / expected) * 100
                print(f"   Expected: {expected} videos ({stats['total_pages']} pages × {stats['videos_per_page']} videos/page)")
                print(f"   Actual: {actual} videos")
                print(f"   Coverage: {percentage:.1f}%")

                if percentage >= 95:
                    print("   ✅ Excellent coverage!")
                elif percentage >= 80:
                    print("   ✅ Good coverage!")
                else:
                    print("   ⚠️  Low coverage - some videos may be missing")

        else:
            print("\n❌ No videos were scraped. Please check:")
            print("1. Your cookies.txt file contains valid session cookies")
            print("2. Your proxy settings (if using proxy)")
            print("3. Your internet connection")
            print("4. The website is accessible")

    except KeyboardInterrupt:
        print("\n\n⏹️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()
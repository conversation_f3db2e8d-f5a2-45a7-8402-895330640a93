#!/usr/bin/env python3
"""
Test script to verify the HTML parsing functionality using the saved HTML file.
This allows testing without needing valid cookies.
"""

import os
from main import parse_videos_from_html, get_total_pages

def test_with_saved_html():
    """Test the parser with the saved HTML file."""
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    
    if not html_files:
        print("No HTML files found. Please save a page from your browser first.")
        return
    
    # Use the first HTML file found
    html_file = html_files[0]
    print(f"Testing with: {html_file}")
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print("Parsing videos from HTML...")
        videos = parse_videos_from_html(html_content)
        
        print(f"Found {len(videos)} videos")
        
        if videos:
            print("\nSample videos:")
            for i, video in enumerate(videos[:3], 1):
                print(f"{i}. {video['title'][:60]}...")
                print(f"   URL: {video['url']}")
                print(f"   Views: {video['views']} | Likes: {video['likes']}")
                print(f"   Video ID: {video['video_id']}")
                print()
        
        # Test pagination parsing
        total_pages = get_total_pages(html_content)
        print(f"Total pages detected: {total_pages}")
        
        return videos
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("HTML Parser Test")
    print("=" * 40)
    
    videos = test_with_saved_html()
    
    if videos:
        print(f"\n✅ Parser test successful! Found {len(videos)} videos")
        
        # Test saving functionality
        from main import save_videos_to_files
        save_videos_to_files(videos, "test_output")
        print("✅ File saving test successful!")
    else:
        print("❌ Parser test failed")

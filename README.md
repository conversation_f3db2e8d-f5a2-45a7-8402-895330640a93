# Jable.TV Watch Later Scraper

A Python script to scrape your "Watch Later" list from Jable.TV and save it to multiple file formats (JSON, CSV, TXT).

## Features

- ✅ Scrapes all pages of your watch later list (handles 20+ pages automatically)
- ✅ Bypasses Cloudflare protection using cloudscraper
- ✅ Uses your browser cookies for authentication
- ✅ Supports proxy configuration
- ✅ Extracts comprehensive video information:
  - Video ID
  - Title
  - URL
  - View count
  - Like count
  - Thumbnail URL
- ✅ Saves data in multiple formats (JSON, CSV, TXT)
- ✅ Includes error handling and retry logic
- ✅ Progress tracking for multi-page scraping

## Requirements

- Python 3.6+
- Required packages (automatically installed):
  - `cloudscraper`
  - `beautifulsoup4`

## Installation

1. Clone or download this repository
2. Install dependencies:
```bash
pip install cloudscraper beautifulsoup4
```

## Setup

### 1. Export Your Browser Cookies

You need to export your Jable.TV session cookies to authenticate with the site.

#### Method 1: Using Browser Extension (Recommended)
1. Install a cookie export extension like "Get cookies.txt LOCALLY" for Chrome/Firefox
2. Go to https://jable.tv and make sure you're logged in
3. Use the extension to export cookies as `cookies.txt`
4. Place the `cookies.txt` file in the same directory as `main.py`

#### Method 2: Manual Export
1. Open Developer Tools (F12) in your browser
2. Go to https://jable.tv/my/favourites/videos-watch-later/
3. In the Network tab, find any request to jable.tv
4. Copy the Cookie header value
5. Create a `cookies.txt` file in Netscape format

### 2. Configure Proxy (Optional)

If you need to use a proxy, edit the `YOUR_PROXY_ADDRESS` variable in `main.py`:

```python
YOUR_PROXY_ADDRESS = "************:7890"  # Your proxy IP:PORT
```

Set to `None` if you don't need a proxy:

```python
YOUR_PROXY_ADDRESS = None
```

## Usage

1. Make sure your `cookies.txt` file is properly configured
2. Run the script:

```bash
python main.py
```

The script will:
1. Load your cookies and initialize the scraper
2. Fetch the first page to determine total pages
3. Scrape all pages of your watch later list
4. Save the results to multiple file formats

## Output Files

The script generates four files with timestamps:

1. **JSON file** (`jable_watch_later_YYYYMMDD_HHMMSS.json`)
   - Complete structured data
   - Easy to process programmatically

2. **CSV file** (`jable_watch_later_YYYYMMDD_HHMMSS.csv`)
   - Spreadsheet-compatible format
   - Can be opened in Excel, Google Sheets, etc.

3. **TXT file** (`jable_watch_later_YYYYMMDD_HHMMSS.txt`)
   - Human-readable detailed format
   - Easy to browse and share

4. **Video Codes file** (`jable_watch_later_codes_YYYYMMDD_HHMMSS.txt`) **NEW!**
   - Contains only video codes (e.g., SONE-749, DASS-654)
   - One code per line
   - Perfect for quick reference or importing into other tools

## Sample Output

```
JABLE.TV WATCH LATER SCRAPER
================================================================================
Loading cookies from cookies.txt...
Cookies loaded successfully.
Fetching first page to determine pagination...
Found 24 videos on page 1
Total pages to scrape: 26
Expected total videos: 624 (if all pages have 24 videos)

Scraping page 2/26...
Found 24 videos on page 2
...

============================================================
SCRAPING COMPLETED!
============================================================
Total pages: 26
Pages scraped successfully: 26
Pages failed: 0
Success rate: 100.0%
Expected videos: 624
Actual videos found: 605

✅ Good result! Got most expected videos.

📊 DETAILED STATISTICS:
   Total pages processed: 26/26
   Average videos per page: 23.3
   Success rate: 100.0%

💾 Saving files...
Saved 605 videos to jable_watch_later_20241217_143022.json
Saved 605 videos to jable_watch_later_20241217_143022.csv
Saved 605 videos to jable_watch_later_20241217_143022.txt
Saved 605 video codes to jable_watch_later_codes_20241217_143022.txt

🔍 VALIDATION:
   Expected: 624 videos (26 pages × 24 videos/page)
   Actual: 605 videos
   Coverage: 97.0%
   ✅ Excellent coverage!
```

## Troubleshooting

### Common Issues

1. **"Login required" error**
   - Your cookies have expired
   - Export fresh cookies from your browser
   - Make sure you're logged in to Jable.TV

2. **"Cloudflare challenge detected"**
   - The site is blocking automated requests
   - Try using a different proxy
   - Wait a few minutes and try again

3. **No videos found**
   - Check if your watch later list is actually populated
   - Verify the cookies are from the correct domain (jable.tv)

4. **Connection errors**
   - Check your internet connection
   - Verify proxy settings if using one
   - The site might be temporarily down

### Debug Mode

The script includes detailed logging. Check the console output for specific error messages.

## Important Notes

- ⚠️ **Respect the website's terms of service**
- ⚠️ **Don't run the script too frequently** (includes 2-second delays between pages)
- ⚠️ **Keep your cookies secure** - they provide access to your account
- ⚠️ **This is for personal use only** - don't redistribute scraped content

## Legal Disclaimer

This tool is for personal use only. Users are responsible for complying with Jable.TV's terms of service and applicable laws. The authors are not responsible for any misuse of this tool.

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure your cookies are valid and recent
3. Verify your internet connection and proxy settings
4. Check the console output for specific error messages

import cloudscraper
import json
from http.cookiejar import MozillaCookieJar
import os

def get_jable_tv_data(api_url, cookies_file='cookies.txt', proxy_address=None):
    """
    Attempts to fetch data from a jable.tv API endpoint,
    bypassing Cloudflare and using pre-existing cookies.
    This version includes increased delay, debug output for cloudscraper,
    and proxy support.

    Args:
        api_url (str): The URL of the API endpoint.
        cookies_file (str): The path to the cookies.txt file.
        proxy_address (str, optional): The proxy address in the format 'IP:PORT'.
                                       e.g., '************:7890'. Defaults to None.

    Returns:
        dict or None: The parsed JSON data if successful, otherwise None.
    """
    # Configure proxies dictionary if a proxy address is provided
    proxies = None
    if proxy_address:
        if not proxy_address.startswith(('http://', 'https://', 'socks5://', 'socks4://')):
            # Assume it's a standard HTTP/HTTPS proxy if no scheme is provided
            proxy_url = f"http://{proxy_address}"
        else:
            proxy_url = proxy_address # Use as is if scheme already included

        proxies = {
            "http": proxy_url,
            "https": proxy_url,
        }
        print(f"Configuring proxy: {proxy_url}")

    # 1. Initialize cloudscraper with increased delay, debug output, and proxies
    print("Initializing cloudscraper with increased delay and debug mode...")
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'desktop': True
        },
        delay=25, # Increased delay from 10 to 25 seconds
        debug=True, # Enable debug output for more information
        proxies=proxies # Pass the proxies dictionary here
    )
    print("Cloudscraper initialized.")

    # 2. Load existing cookies if the file exists
    if os.path.exists(cookies_file):
        print(f"Loading cookies from {cookies_file}...")
        cookie_jar = MozillaCookieJar(cookies_file)
        try:
            cookie_jar.load(ignore_discard=True, ignore_expires=True)
            scraper.cookies.update(cookie_jar)
            print("Cookies loaded successfully and added to scraper session.")
        except Exception as e:
            print(f"Error loading cookies from {cookies_file}: {e}")
            print("Proceeding without pre-loaded cookies for now.")
    else:
        print(f"Cookies file '{cookies_file}' not found. Proceeding without pre-loading cookies.")

    # 3. Make the request
    print(f"Attempting to fetch data from: {api_url}")
    try:
        response = scraper.get(api_url)
        response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)

        # 4. Check for Cloudflare challenge remaining
        if "Just a moment..." in response.text or "Enable JavaScript and cookies to continue" in response.text:
            print("\n------------------------------------------------------------")
            print("WARNING: Cloudflare challenge might still be active!")
            print("This could mean cloudscraper couldn't solve it, or a new challenge appeared.")
            print("Response content (first 500 chars):\n", response.text[:500])
            print("------------------------------------------------------------\n")
            return None

        # 5. Parse the JSON response
        try:
            data = json.loads(response.text)
            print("Data fetched and parsed successfully!")
            return data
        except json.JSONDecodeError:
            print("\n------------------------------------------------------------")
            print("ERROR: Failed to decode JSON. Response might not be valid JSON.")
            print("This often happens if the page returned is an error page or a Cloudflare page.")
            print("Response content (first 500 chars):\n", response.text[:500])
            print("------------------------------------------------------------\n")
            return None

    except cloudscraper.exceptions.CloudflareChallengeError as e:
        print(f"\n------------------------------------------------------------")
        print(f"Cloudflare challenge failed with explicit error: {e}")
        print("This means cloudscraper identified a challenge but couldn't solve it.")
        print("------------------------------------------------------------\n")
        return None
    except Exception as e:
        print(f"\n------------------------------------------------------------")
        print(f"An unexpected error occurred during the request: {e}")
        print("------------------------------------------------------------\n")
        return None

# --- Main execution ---
if __name__ == "__main__":
    API_URL = "https://jable.tv/api/my/favourites/videos-watch-later"
    COOKIES_FILE = 'cookies.txt'
    # Define your proxy address here:
    YOUR_PROXY_ADDRESS = "************:7890" # <--- YOUR PROXY HERE

    if not os.path.exists(COOKIES_FILE):
        print(f"Creating a dummy '{COOKIES_FILE}' for demonstration purposes.")
        print(f"PLEASE NOTE: This dummy file contains NO actual cookies and will NOT bypass authentication.")
        print(f"You NEED to replace its content with valid cookies exported from your browser.")
        with open(COOKIES_FILE, 'w') as f:
            f.write("# Netscape HTTP Cookie File\n")
            f.write("# This is a dummy file. Replace with real cookies from jable.tv after login.\n")
            f.write(".jable.tv\tTRUE\t/\tFALSE\t0\t__cf_bm\t1234567890abcdef\n")
            f.write(".jable.tv\tTRUE\t/\tFALSE\t0\tPHPSESSID\tabcdef1234567890\n")
        print("Dummy cookies.txt created.")
    else:
        print(f"'{COOKIES_FILE}' already exists. Ensure it contains valid cookies from your jable.tv session.")

    print("\n--- Starting Jable.tv API Data Retrieval ---")
    # Pass the proxy_address argument to the function
    data = get_jable_tv_data(API_URL, COOKIES_FILE, proxy_address=YOUR_PROXY_ADDRESS)

    if data:
        print("\n--- Retrieved Data Snippet ---")
        if isinstance(data, list):
            print(f"Total items found: {len(data)}")
            for i, item in enumerate(data[:5]):
                print(f"  Item {i+1}:")
                print(f"    Title: {item.get('title', 'N/A')}")
                print(f"    Views: {item.get('views', 'N/A')}")
                print(f"    Duration: {item.get('duration', 'N/A')}")
        elif isinstance(data, dict):
            print(json.dumps(data, indent=2)[:1000] + "\n...")
        else:
            print(f"Unexpected data format: {type(data)}")
    else:
        print("\n--- Failed to retrieve data. Please review the output above for errors. ---")
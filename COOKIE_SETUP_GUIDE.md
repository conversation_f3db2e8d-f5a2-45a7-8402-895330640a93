# 🍪 Cookie Setup Guide for Jable.TV Scraper

The script is failing because it needs your **real browser cookies** to authenticate with Jable.TV. The current `cookies.txt` file contains dummy data.

## 🚨 Why You're Getting 403 Forbidden Error

The error message shows:
```
> HTTP/1.1 403 Forbidden
> Cf-Mitigated: challenge
```

This means Cloudflare is blocking the request because:
1. **No valid authentication cookies** - You're not logged in
2. **Cloudflare protection** - The site requires valid session data

## 📋 Step-by-Step Cookie Export

### Method 1: Using Browser Extension (Recommended)

1. **Install Cookie Extension**:
   - Chrome: [Get cookies.txt LOCALLY](https://chrome.google.com/webstore/detail/get-cookiestxt-locally/cclelndahbckbenkjhflpdbgdldlbecc)
   - Firefox: [cookies.txt](https://addons.mozilla.org/en-US/firefox/addon/cookies-txt/)

2. **Login to Jable.TV**:
   - Go to https://jable.tv
   - Login with your account
   - Navigate to https://jable.tv/my/favourites/videos-watch-later/
   - Make sure you can see your watch later list

3. **Export Cookies**:
   - Click the extension icon
   - Select "Export cookies for this site"
   - Save as `cookies.txt` in your project folder

### Method 2: Manual Export (Advanced)

1. **Open Developer Tools** (F12)
2. **Go to Application/Storage tab**
3. **Find Cookies section** → jable.tv
4. **Copy all cookies** and format them as Netscape format

## 🔧 Required Cookies

Make sure your `cookies.txt` contains these essential cookies:
- `PHPSESSID` - Session ID
- `__cf_bm` - Cloudflare bot management
- Any authentication tokens

## ✅ Test Your Cookies

After exporting cookies, test with:
```bash
python main.py
```

You should see:
```
Loading cookies from cookies.txt...
Cookies loaded successfully.
Fetching first page to determine pagination...
Found 24 videos on page 1
Total pages to scrape: 26
```

## 🚫 Common Issues

### Issue 1: Still Getting 403
- **Solution**: Re-export cookies (they might have expired)
- **Check**: Make sure you're logged in when exporting

### Issue 2: "Login required" message
- **Solution**: Your session expired, login again and re-export

### Issue 3: Empty cookies.txt
- **Solution**: Make sure the extension exported to the right location

## 🔒 Security Note

- **Keep cookies.txt private** - It contains your login session
- **Don't share cookies.txt** - Anyone with it can access your account
- **Re-export regularly** - Cookies expire after some time

## 🎯 Expected Result

Once cookies are working, you should get:
```
✅ Successfully scraped 605+ videos!
Saved 605 videos to jable_watch_later_YYYYMMDD_HHMMSS.json
Saved 605 videos to jable_watch_later_YYYYMMDD_HHMMSS.csv  
Saved 605 videos to jable_watch_later_YYYYMMDD_HHMMSS.txt
Saved 605 video codes to jable_watch_later_codes_YYYYMMDD_HHMMSS.txt
```

## 🆘 Still Not Working?

If you continue having issues:
1. Try without proxy (set `YOUR_PROXY_ADDRESS = None`)
2. Use a different browser to export cookies
3. Clear browser cache and login fresh
4. Check if the website is accessible in your browser
